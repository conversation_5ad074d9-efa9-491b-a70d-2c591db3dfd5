﻿<?xml version="1.0" encoding="utf-8" ?>
<local:ProfileFormViewBase
    x:Class="Platform.Client.Common.Features.Account.ProfileFormView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Account"
    Title="My Profile"
    x:DataType="local:ProfileFormView"
    Shell.NavBarIsVisible="False">

    <Grid Padding="16"  >
        <!--  Content Section  -->
        <ScrollView Grid.Row="1">
            <VerticalStackLayout Spacing="0">

                <!--  Error Message  -->
                <Border
                    Margin="16,16,16,0"
                    BackgroundColor="{AppThemeBinding Light={StaticResource Red50},
                                                      Dark={StaticResource Red900}}"
                    IsVisible="{Binding HasError}"
                    Stroke="{AppThemeBinding Light={StaticResource Red200},
                                             Dark={StaticResource Red700}}"
                    StrokeThickness="1">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="8" />
                    </Border.StrokeShape>
                    <Grid Padding="12" ColumnDefinitions="Auto,*">
                        <Image
                            Grid.Column="0"
                            Margin="0,2,8,0"
                            VerticalOptions="Start">
                            <Image.Source>
                                <FontImageSource
                                    FontFamily="Jelly"
                                    Glyph="&#xf071;"
                                    Size="16"
                                    Color="{AppThemeBinding Light={StaticResource Red600},
                                                            Dark={StaticResource Red400}}" />
                            </Image.Source>
                        </Image>
                        <Label
                            Grid.Column="1"
                            FontSize="14"
                            Text="{Binding Error}"
                            TextColor="{AppThemeBinding Light={StaticResource Red600},
                                                        Dark={StaticResource Red400}}" />
                    </Grid>
                </Border>

                <!--  Profile Display Section  -->
                <Border
                    Margin="0,16,0,0"
                    BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                                      Dark={StaticResource Gray800}}"
                    StrokeThickness="0">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="0" />
                    </Border.StrokeShape>
                    <Grid
                        Padding="16"
                        ColumnDefinitions="Auto,*"
                        RowDefinitions="Auto">

                        <!--  Avatar Display  -->
                        <Border
                            Grid.Column="0"
                            BackgroundColor="Transparent"
                            HeightRequest="100"
                            Stroke="{AppThemeBinding Light={StaticResource Gray400},
                                                     Dark={StaticResource Gray300}}"
                            StrokeThickness="2"
                            VerticalOptions="Start"
                            WidthRequest="100">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="50" />
                            </Border.StrokeShape>
                            <Grid>
                                <Image
                                    Aspect="AspectFill"
                                    IsVisible="{Binding SelectedItem.AvatarData, Converter={StaticResource StringToBoolConverter}}"
                                    Source="{Binding SelectedItem.AvatarData}" />
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="32"
                                    HorizontalOptions="Center"
                                    IsVisible="{Binding SelectedItem.AvatarData, Converter={StaticResource InverseStringToBoolConverter}}"
                                    Text="{Binding SelectedItem.DisplayName, Converter={StaticResource InitialsConverter}}"
                                    TextColor="{AppThemeBinding Light={StaticResource Blue600},
                                                                Dark={StaticResource Blue400}}"
                                    VerticalOptions="Center" />
                            </Grid>
                        </Border>

                        <!--  Name and Info  -->
                        <VerticalStackLayout
                            Grid.Column="1"
                            Margin="16,0,0,0"
                            Spacing="4"
                            VerticalOptions="Center">
                            <Label
                                FontAttributes="Bold"
                                FontSize="20"
                                LineBreakMode="TailTruncation"
                                Text="{Binding SelectedItem.DisplayName, TargetNullValue='No Name'}"
                                TextColor="{AppThemeBinding Light={StaticResource Gray900},
                                                            Dark={StaticResource Gray100}}" />
                            <Label
                                FontSize="14"
                                Text="Profile Information"
                                TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                            Dark={StaticResource Gray400}}" />
                        </VerticalStackLayout>
                    </Grid>
                </Border>

                <!--  Form Fields Section  -->
                <VerticalStackLayout
                    Margin="0,8,0,0"
                    Padding="16"
                    BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                                      Dark={StaticResource Gray800}}"
                    Spacing="24">

                    <!--  Avatar Selection  -->
                    <VerticalStackLayout Spacing="8">
                        <Label
                            FontAttributes="Bold"
                            FontSize="16"
                            Text="Select Avatar"
                            TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                        Dark={StaticResource Gray200}}" />
                        <Label
                            Margin="0,0,0,8"
                            FontSize="12"
                            Text="Choose from predefined avatar images"
                            TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                        Dark={StaticResource Gray400}}" />

                        <Picker
                            Title="Choose an avatar..."
                            FontSize="14"
                            ItemsSource="{Binding AvatarOptions}"
                            SelectedItem="{Binding SelectedItem.AvatarData}"
                            TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                        Dark={StaticResource Gray200}}"
                            TitleColor="{AppThemeBinding Light={StaticResource Gray400},
                                                         Dark={StaticResource Gray500}}" />
                    </VerticalStackLayout>

                    <!--  Display Name  -->
                    <VerticalStackLayout Spacing="8">
                        <Label
                            FontAttributes="Bold"
                            FontSize="16"
                            Text="Display Name"
                            TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                        Dark={StaticResource Gray200}}" />
                        <Label
                            Margin="0,0,0,8"
                            FontSize="12"
                            LineBreakMode="WordWrap"
                            Text="This name will be visible to your friends when you add them as your friend"
                            TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                        Dark={StaticResource Gray400}}" />
                        <Entry
                            BackgroundColor="Transparent"
                            FontSize="16"
                            Placeholder="Enter your display name"
                            PlaceholderColor="{AppThemeBinding Light={StaticResource Gray400},
                                                               Dark={StaticResource Gray500}}"
                            Text="{Binding SelectedItem.DisplayName}"
                            TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                        Dark={StaticResource Gray200}}" />
                    </VerticalStackLayout>

                    <!--  Avatar Description (if needed for AI generation)  -->
                    <VerticalStackLayout IsVisible="False" Spacing="8">
                        <Label
                            FontAttributes="Bold"
                            FontSize="16"
                            Text="Avatar Description"
                            TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                        Dark={StaticResource Gray200}}" />
                        <Label
                            Margin="0,0,0,8"
                            FontSize="12"
                            Text="Description used for AI avatar generation (optional)"
                            TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                        Dark={StaticResource Gray400}}" />
                        <Border
                            BackgroundColor="{AppThemeBinding Light={StaticResource Gray50},
                                                              Dark={StaticResource Gray700}}"
                            HeightRequest="80"
                            Stroke="{AppThemeBinding Light={StaticResource Gray200},
                                                     Dark={StaticResource Gray600}}"
                            StrokeThickness="1">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="8" />
                            </Border.StrokeShape>
                            <Editor
                                Margin="12,8"
                                AutoSize="TextChanges"
                                BackgroundColor="Transparent"
                                FontSize="16"
                                MaxLength="450"
                                Placeholder="Describe the avatar you want to generate..."
                                PlaceholderColor="{AppThemeBinding Light={StaticResource Gray400},
                                                                   Dark={StaticResource Gray500}}"
                                Text="{Binding SelectedItem.AvatarDescription}"
                                TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                            Dark={StaticResource Gray200}}" />
                        </Border>
                    </VerticalStackLayout>

                    <!--  Loading Indicator  -->
                    <ActivityIndicator
                        Margin="0,16"
                        IsRunning="{Binding IsWorking}"
                        IsVisible="{Binding IsWorking}"
                        Color="{AppThemeBinding Light={StaticResource Blue600},
                                                Dark={StaticResource Blue400}}" />

                </VerticalStackLayout>

                <Button
                    Margin="48,16"
                    Command="{Binding SaveCommand}"
                    IsEnabled="{Binding IsWorking, Converter={StaticResource InverseBoolConverter}}"
                    Text="{Binding IsWorking, Converter={StaticResource BoolToStringConverter}, ConverterParameter='Saving...|Update'}"
                    TextColor="{AppThemeBinding Light={StaticResource White},
                                                Dark={StaticResource White}}" />
            </VerticalStackLayout>
        </ScrollView>

        <Button
            Margin="8"
            Padding="8"
            Background="Transparent"
            Clicked="Button_Clicked"
            HeightRequest="40"
            HorizontalOptions="End"
            VerticalOptions="Start"
            WidthRequest="40">

            <Button.ImageSource>
                <FontImageSource
                    FontFamily="Jelly"
                    Glyph="&#xf057;"
                    Size="20"
                    Color="{AppThemeBinding Light={StaticResource Gray700},
                                            Dark={StaticResource Gray300}}" />
            </Button.ImageSource>
        </Button>
    </Grid>
</local:ProfileFormViewBase>
